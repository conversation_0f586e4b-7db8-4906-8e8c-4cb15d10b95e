/**
 * 安全的本地存储工具类
 * 处理微信小程序存储相关的错误和异常
 */

class StorageUtil {
  /**
   * 安全地设置同步存储
   * @param {string} key 存储键
   * @param {any} data 存储数据
   * @returns {boolean} 是否成功
   */
  static setStorageSync(key, data) {
    try {
      wx.setStorageSync(key, data);
      return true;
    } catch (error) {
      console.error(`设置存储失败 [${key}]:`, error);
      
      // 如果是登录相关错误，尝试重新登录
      if (error.message && error.message.includes('INVALID_LOGIN')) {
        console.log('检测到登录状态过期，尝试重新登录...');
        this.reLogin();
      }
      
      return false;
    }
  }

  /**
   * 安全地获取同步存储
   * @param {string} key 存储键
   * @param {any} defaultValue 默认值
   * @returns {any} 存储的数据或默认值
   */
  static getStorageSync(key, defaultValue = null) {
    try {
      const data = wx.getStorageSync(key);
      return data !== '' ? data : defaultValue;
    } catch (error) {
      console.error(`获取存储失败 [${key}]:`, error);
      
      // 如果是登录相关错误，尝试重新登录
      if (error.message && error.message.includes('INVALID_LOGIN')) {
        console.log('检测到登录状态过期，尝试重新登录...');
        this.reLogin();
      }
      
      return defaultValue;
    }
  }

  /**
   * 安全地设置异步存储
   * @param {string} key 存储键
   * @param {any} data 存储数据
   * @returns {Promise} Promise对象
   */
  static setStorage(key, data) {
    return new Promise((resolve, reject) => {
      wx.setStorage({
        key: key,
        data: data,
        success: () => {
          resolve(true);
        },
        fail: (error) => {
          console.error(`异步设置存储失败 [${key}]:`, error);
          
          if (error.errMsg && error.errMsg.includes('INVALID_LOGIN')) {
            console.log('检测到登录状态过期，尝试重新登录...');
            this.reLogin();
          }
          
          reject(error);
        }
      });
    });
  }

  /**
   * 安全地获取异步存储
   * @param {string} key 存储键
   * @param {any} defaultValue 默认值
   * @returns {Promise} Promise对象
   */
  static getStorage(key, defaultValue = null) {
    return new Promise((resolve, reject) => {
      wx.getStorage({
        key: key,
        success: (res) => {
          resolve(res.data);
        },
        fail: (error) => {
          console.error(`异步获取存储失败 [${key}]:`, error);
          
          if (error.errMsg && error.errMsg.includes('INVALID_LOGIN')) {
            console.log('检测到登录状态过期，尝试重新登录...');
            this.reLogin();
          }
          
          resolve(defaultValue);
        }
      });
    });
  }

  /**
   * 重新登录
   */
  static reLogin() {
    wx.login({
      success: (res) => {
        if (res.code) {
          console.log('重新登录成功，获取新的登录凭证：', res.code);
          
          // 更新全局数据
          const app = getApp();
          if (app && app.globalData) {
            app.globalData.code = res.code;
          }
        } else {
          console.error('重新登录失败：', res.errMsg);
        }
      },
      fail: (error) => {
        console.error('重新登录失败：', error);
      }
    });
  }

  /**
   * 清除所有存储数据
   */
  static clearStorage() {
    try {
      wx.clearStorageSync();
      console.log('清除存储成功');
      return true;
    } catch (error) {
      console.error('清除存储失败:', error);
      return false;
    }
  }

  /**
   * 获取存储信息
   */
  static getStorageInfo() {
    try {
      const info = wx.getStorageInfoSync();
      console.log('存储信息:', info);
      return info;
    } catch (error) {
      console.error('获取存储信息失败:', error);
      return null;
    }
  }

  /**
   * 移除指定存储
   * @param {string} key 存储键
   */
  static removeStorage(key) {
    try {
      wx.removeStorageSync(key);
      console.log(`移除存储成功 [${key}]`);
      return true;
    } catch (error) {
      console.error(`移除存储失败 [${key}]:`, error);
      return false;
    }
  }
}

module.exports = StorageUtil;
