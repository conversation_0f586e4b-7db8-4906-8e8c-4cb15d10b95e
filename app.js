// app.js
const StorageUtil = require('./utils/storage.js');

App(
  {
    towxml: require('/towxml/index'),
  onLaunch() {
    //获取本地缓存是否已经登录
    // wx.getStorage('islogin');
    // console.log('islogin_inapp',islogin)
    //获取系统信息
    const systemInfo=wx.getSystemInfoSync();
    console.log('systemInfo',systemInfo)
    this.globalData.safeArea=systemInfo.safeArea;

    // 先进行登录，然后再处理本地存储
    wx.login({
      success: res => {
        var code=res.code;
        if(code){
          console.log('获取用户登录凭证：'+code);
          this.globalData.code=code;

          // 登录成功后再进行本地存储操作
          const logs = StorageUtil.getStorageSync('logs', []);
          logs.unshift(Date.now());
          StorageUtil.setStorageSync('logs', logs);
        }
        else{
          console.log('用户登录状态失败'+res.errMsg)
        }
      },
      fail: err => {
        console.error('登录失败:', err);
        // 即使登录失败，也尝试基本的存储操作
        const logs = StorageUtil.getStorageSync('logs', []);
        logs.unshift(Date.now());
        StorageUtil.setStorageSync('logs', logs);
      }
    })
  },
  globalData: {
    islogin:false,
    userInfo: {},
    code:'',
    token: '',
    safeArea:{},
    openid:''
  }
})
