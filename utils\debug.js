/**
 * 调试和故障排除工具
 */

class DebugUtil {
  /**
   * 检查小程序环境
   */
  static checkEnvironment() {
    const env = {
      platform: '',
      version: '',
      SDKVersion: '',
      system: '',
      model: '',
      brand: '',
      language: '',
      pixelRatio: 0,
      screenWidth: 0,
      screenHeight: 0,
      windowWidth: 0,
      windowHeight: 0,
      statusBarHeight: 0,
      safeArea: {},
      albumAuthorized: false,
      cameraAuthorized: false,
      locationAuthorized: false,
      microphoneAuthorized: false,
      notificationAuthorized: false,
      bluetoothEnabled: false,
      locationEnabled: false,
      wifiEnabled: false
    };

    try {
      const systemInfo = wx.getSystemInfoSync();
      Object.assign(env, systemInfo);
      console.log('环境检查完成:', env);
      return env;
    } catch (error) {
      console.error('环境检查失败:', error);
      return null;
    }
  }

  /**
   * 检查网络状态
   */
  static checkNetwork() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          console.log('网络类型:', res.networkType);
          resolve({
            networkType: res.networkType,
            isConnected: res.networkType !== 'none'
          });
        },
        fail: (error) => {
          console.error('获取网络状态失败:', error);
          resolve({
            networkType: 'unknown',
            isConnected: false
          });
        }
      });
    });
  }

  /**
   * 检查存储状态
   */
  static checkStorage() {
    try {
      const info = wx.getStorageInfoSync();
      console.log('存储信息:', info);
      
      // 检查存储使用情况
      const usage = {
        keys: info.keys,
        currentSize: info.currentSize,
        limitSize: info.limitSize,
        usagePercent: Math.round((info.currentSize / info.limitSize) * 100)
      };
      
      console.log('存储使用情况:', usage);
      return usage;
    } catch (error) {
      console.error('检查存储状态失败:', error);
      return null;
    }
  }

  /**
   * 测试登录功能
   */
  static testLogin() {
    return new Promise((resolve) => {
      console.log('开始测试登录...');
      
      wx.login({
        success: (res) => {
          if (res.code) {
            console.log('登录测试成功, code:', res.code);
            resolve({
              success: true,
              code: res.code,
              message: '登录成功'
            });
          } else {
            console.error('登录测试失败, 未获取到code:', res.errMsg);
            resolve({
              success: false,
              code: null,
              message: res.errMsg || '未获取到登录凭证'
            });
          }
        },
        fail: (error) => {
          console.error('登录测试失败:', error);
          resolve({
            success: false,
            code: null,
            message: error.errMsg || '登录失败'
          });
        }
      });
    });
  }

  /**
   * 测试存储功能
   */
  static testStorage() {
    const testKey = 'debug_test_' + Date.now();
    const testData = { test: true, timestamp: Date.now() };
    
    try {
      // 测试同步存储
      wx.setStorageSync(testKey, testData);
      const retrieved = wx.getStorageSync(testKey);
      
      // 清理测试数据
      wx.removeStorageSync(testKey);
      
      const success = JSON.stringify(retrieved) === JSON.stringify(testData);
      console.log('存储测试结果:', success);
      
      return {
        success: success,
        message: success ? '存储功能正常' : '存储数据不匹配'
      };
    } catch (error) {
      console.error('存储测试失败:', error);
      return {
        success: false,
        message: error.message || '存储测试失败'
      };
    }
  }

  /**
   * 综合诊断
   */
  static async diagnose() {
    console.log('开始系统诊断...');
    
    const diagnosis = {
      timestamp: new Date().toISOString(),
      environment: this.checkEnvironment(),
      network: await this.checkNetwork(),
      storage: this.checkStorage(),
      storageTest: this.testStorage(),
      login: await this.testLogin()
    };
    
    console.log('诊断完成:', diagnosis);
    
    // 生成诊断报告
    const report = this.generateReport(diagnosis);
    console.log('诊断报告:', report);
    
    return diagnosis;
  }

  /**
   * 生成诊断报告
   */
  static generateReport(diagnosis) {
    const issues = [];
    const suggestions = [];
    
    // 检查网络
    if (!diagnosis.network.isConnected) {
      issues.push('网络连接异常');
      suggestions.push('请检查网络连接');
    }
    
    // 检查存储
    if (!diagnosis.storageTest.success) {
      issues.push('存储功能异常');
      suggestions.push('请清理小程序缓存或重启小程序');
    }
    
    if (diagnosis.storage && diagnosis.storage.usagePercent > 80) {
      issues.push('存储空间不足');
      suggestions.push('请清理不必要的缓存数据');
    }
    
    // 检查登录
    if (!diagnosis.login.success) {
      issues.push('登录功能异常');
      if (diagnosis.login.message.includes('INVALID_LOGIN')) {
        suggestions.push('请重启微信开发者工具');
        suggestions.push('请检查小程序配置');
      } else {
        suggestions.push('请检查网络连接');
        suggestions.push('请稍后重试');
      }
    }
    
    return {
      status: issues.length === 0 ? 'healthy' : 'issues',
      issues: issues,
      suggestions: suggestions,
      summary: issues.length === 0 ? '系统运行正常' : `发现 ${issues.length} 个问题`
    };
  }

  /**
   * 清理缓存
   */
  static clearCache() {
    try {
      // 获取所有存储的key
      const info = wx.getStorageInfoSync();
      const keysToRemove = info.keys.filter(key => 
        key.startsWith('cache_') || 
        key.startsWith('temp_') ||
        key.startsWith('debug_')
      );
      
      // 清理缓存数据
      keysToRemove.forEach(key => {
        try {
          wx.removeStorageSync(key);
        } catch (error) {
          console.error(`清理缓存失败 [${key}]:`, error);
        }
      });
      
      console.log(`清理了 ${keysToRemove.length} 个缓存项`);
      return {
        success: true,
        clearedCount: keysToRemove.length
      };
    } catch (error) {
      console.error('清理缓存失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 显示调试信息
   */
  static showDebugInfo() {
    this.diagnose().then(diagnosis => {
      const report = this.generateReport(diagnosis);
      
      let message = `状态: ${report.summary}\n`;
      if (report.issues.length > 0) {
        message += `问题: ${report.issues.join(', ')}\n`;
        message += `建议: ${report.suggestions.join(', ')}`;
      }
      
      wx.showModal({
        title: '系统诊断',
        content: message,
        showCancel: false
      });
    });
  }
}

module.exports = DebugUtil;
