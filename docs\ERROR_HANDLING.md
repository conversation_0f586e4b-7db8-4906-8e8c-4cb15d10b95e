# 错误处理和调试指南

## 常见错误解决方案

### 1. 登录Token过期错误

**错误信息：**
```
Error: INVALID_LOGIN,access_token expired
```

**问题描述：**
- 微信小程序的登录状态过期
- 通常发生在调用 `wx.setStorageSync()` 或其他需要登录状态的API时

**解决方案：**
1. **使用安全存储工具类** - 已创建 `utils/storage.js`
2. **调整登录时序** - 先登录再进行存储操作
3. **添加错误捕获** - 使用 try-catch 包装存储操作

**修复代码示例：**
```javascript
// 旧代码（容易出错）
const logs = wx.getStorageSync('logs') || []
wx.setStorageSync('logs', logs)

// 新代码（安全）
const StorageUtil = require('./utils/storage.js');
const logs = StorageUtil.getStorageSync('logs', []);
StorageUtil.setStorageSync('logs', logs);
```

### 2. 存储操作最佳实践

#### 使用安全存储工具类
```javascript
const StorageUtil = require('./utils/storage.js');

// 同步存储
StorageUtil.setStorageSync('key', data);
const data = StorageUtil.getStorageSync('key', defaultValue);

// 异步存储
StorageUtil.setStorage('key', data).then(() => {
  console.log('存储成功');
}).catch(error => {
  console.error('存储失败:', error);
});
```

#### 错误处理策略
1. **自动重试登录** - 检测到登录过期时自动重新登录
2. **降级处理** - 存储失败时使用内存缓存
3. **用户友好提示** - 向用户显示适当的错误信息

### 3. 调试技巧

#### 开发者工具调试
1. 打开微信开发者工具
2. 查看 Console 面板的错误信息
3. 使用 Network 面板检查网络请求
4. 利用 Storage 面板查看本地存储状态

#### 日志记录
```javascript
// 在关键位置添加日志
console.log('开始登录流程');
console.log('登录成功，code:', code);
console.error('存储操作失败:', error);
```

#### 错误监控
```javascript
// 全局错误处理
App({
  onError(error) {
    console.error('全局错误:', error);
    // 可以上报到错误监控服务
  }
});
```

### 4. 性能优化建议

#### 存储优化
1. **减少存储频率** - 批量操作而非频繁单次操作
2. **数据压缩** - 对大数据进行压缩存储
3. **清理过期数据** - 定期清理不需要的存储数据

#### 登录优化
1. **缓存登录状态** - 避免重复登录
2. **静默刷新** - 在后台自动刷新token
3. **错误重试** - 登录失败时自动重试

### 5. 错误代码对照表

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| INVALID_LOGIN | 登录状态无效 | 重新调用 wx.login() |
| access_token expired | 访问令牌过期 | 刷新登录状态 |
| storage quota exceeded | 存储空间不足 | 清理旧数据 |
| network error | 网络错误 | 检查网络连接 |

### 6. 预防措施

#### 代码规范
1. **统一使用存储工具类** - 不直接调用 wx.setStorageSync
2. **添加错误处理** - 所有异步操作都要有错误处理
3. **参数验证** - 验证输入参数的有效性

#### 测试策略
1. **模拟网络异常** - 测试网络断开情况
2. **模拟存储满** - 测试存储空间不足情况
3. **模拟登录过期** - 测试token过期情况

### 7. 监控和告警

#### 错误上报
```javascript
// 错误上报函数
function reportError(error, context) {
  console.error('错误上报:', error, context);
  // 可以集成第三方错误监控服务
  // 如：腾讯云监控、阿里云监控等
}
```

#### 性能监控
```javascript
// 性能监控
function performanceMonitor(operation, startTime) {
  const endTime = Date.now();
  const duration = endTime - startTime;
  console.log(`操作 ${operation} 耗时: ${duration}ms`);
}
```

### 8. 用户体验优化

#### 友好的错误提示
```javascript
function showUserFriendlyError(error) {
  let message = '操作失败，请稍后重试';
  
  if (error.message.includes('INVALID_LOGIN')) {
    message = '登录状态已过期，正在重新登录...';
  } else if (error.message.includes('network')) {
    message = '网络连接异常，请检查网络设置';
  }
  
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
}
```

#### 加载状态管理
```javascript
// 显示加载状态
wx.showLoading({
  title: '加载中...'
});

// 操作完成后隐藏
wx.hideLoading();
```

---

**文档版本：** v1.0  
**最后更新：** 2024年12月  
**维护者：** Shikisuki
